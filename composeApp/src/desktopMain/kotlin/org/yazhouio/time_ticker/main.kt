package org.yazhouio.time_ticker

import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.Text
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.Notification
import androidx.compose.ui.window.rememberTrayState
import java.awt.Desktop

fun main() = application {
    var count by remember { mutableStateOf(0) }
    var isOpen by remember { mutableStateOf(true) }

    if (isOpen) {
        val trayState = rememberTrayState()

        // 应用启动时检查通知支持
        LaunchedEffect(Unit) {
            if (!NotificationManager.isNotificationSupported()) {
                println("系统不支持通知功能")
            } else {
                println("通知功能已就绪")
                // 发送欢迎通知
                NotificationManager.sendNotification(
                    trayState,
                    "Time Ticker",
                    "应用已启动，通知功能正常！",
                    Notification.Type.Info
                )
            }
        }

        Tray(
            state = trayState,
            icon = TrayIcon,
            menu = {
                Item(
                    "增加计数",
                    onClick = {
                        count++
                        NotificationManager.sendNotification(
                            trayState,
                            "计数更新",
                            "当前计数: $count",
                            Notification.Type.Info
                        )
                    }
                )
                Item(
                    "发送测试通知",
                    onClick = {
                        NotificationManager.sendNotification(
                            trayState,
                            "测试通知",
                            "这是一条测试通知消息！时间: ${System.currentTimeMillis()}",
                            Notification.Type.Info
                        )
                    }
                )
                Item(
                    "通知设置帮助",
                    onClick = {
                        NotificationManager.showNotificationHelp()
                    }
                )
                Item(
                    "打开系统通知设置",
                    onClick = {
                        try {
                            if (Desktop.isDesktopSupported()) {
                                val desktop = Desktop.getDesktop()
                                if (desktop.isSupported(Desktop.Action.OPEN)) {
                                    // 在 macOS 上打开通知设置
                                    Runtime.getRuntime().exec("open x-apple.systempreferences:com.apple.preference.notifications")
                                }
                            }
                        } catch (e: Exception) {
                            println("无法打开系统设置: ${e.message}")
                            NotificationManager.showNotificationHelp()
                        }
                    }
                )
                Item(
                    "退出",
                    onClick = {
                        isOpen = false
                    }
                )
            }
        )

        Window(
            onCloseRequest = {
                isOpen = false
            },
            title = "Time Ticker",
            icon = MyAppIcon
        ) {
            // content
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "计数值: $count\n\n点击系统托盘图标查看更多选项")
            }
        }
    }
}

object MyAppIcon : Painter() {
    override val intrinsicSize = Size(256f, 256f)

    override fun DrawScope.onDraw() {
        drawOval(Color.Green, Offset(size.width / 4, 0f), Size(size.width / 2f, size.height))
        drawOval(Color.Blue, Offset(0f, size.height / 4), Size(size.width, size.height / 2f))
        drawOval(Color.Red, Offset(size.width / 4, size.height / 4), Size(size.width / 2f, size.height / 2f))
    }
}

object TrayIcon : Painter() {
    override val intrinsicSize = Size(256f, 256f)

    override fun DrawScope.onDraw() {
        drawOval(Color(0xFFFFA500))
    }
}